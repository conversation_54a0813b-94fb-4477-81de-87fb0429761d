use crate::atoms::Outcome;

use super::super::Atom;
use crate::utilities;
use anyhow::anyhow;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::debug;

#[derive(Default)]
pub struct Exec {
    pub command: String,
    pub arguments: Vec<String>,
    pub working_dir: Option<String>,
    pub environment: Vec<(String, String)>,
    pub privileged: bool,
    pub privilege_provider: String,
    pub(crate) status: ExecStatus,
}

#[derive(Default)]
pub(crate) struct ExecStatus {
    code: i32,
    stdout: String,
    stderr: String,
}

/// Command request sent to elevated helper
#[derive(Debug, Serialize, Deserialize)]
struct CommandRequest {
    id: String,
    command: String,
    arguments: Vec<String>,
    working_dir: Option<String>,
    environment: HashMap<String, String>,
}

/// Command response received from elevated helper
#[derive(Debug, Serialize, Deserialize)]
struct CommandResponse {
    id: String,
    success: bool,
    exit_code: i32,
    stdout: String,
    stderr: String,
    error: Option<String>,
}

#[allow(dead_code)]
pub fn new_run_command(command: String) -> Exec {
    Exec {
        command,
        ..Default::default()
    }
}

impl Exec {
    /// Execute command via elevated helper subprocess
    fn execute_via_elevated_helper(&mut self) -> anyhow::Result<()> {
        let pipe_name = std::env::var("COMTRYA_ELEVATED_PIPE")
            .map_err(|_| anyhow!("Elevated helper pipe not found. Helper may not be running."))?;

        debug!("Sending privileged command to elevated helper via pipe: {}", pipe_name);

        // Create command request
        let request = CommandRequest {
            id: uuid::Uuid::new_v4().to_string(),
            command: self.command.clone(),
            arguments: self.arguments.clone(),
            working_dir: self.working_dir.clone(),
            environment: self.environment.iter().cloned().collect(),
        };

        // Connect to elevated helper
        use interprocess::local_socket::{GenericNamespaced, ToNsName};
        use interprocess::local_socket::traits::Stream;

        let name = pipe_name.to_ns_name::<GenericNamespaced>()
            .map_err(|e| anyhow!("Failed to create namespace name: {}", e))?;

        let mut stream = Stream::connect(name)
            .map_err(|e| anyhow!("Failed to connect to elevated helper: {}", e))?;

        // Send request
        use std::io::Write;
        let request_json = serde_json::to_string(&request)?;
        writeln!(stream, "{}", request_json)?;
        stream.flush()?;

        // Read response
        use std::io::{BufRead, BufReader};
        let mut reader = BufReader::new(&stream);
        let mut response_line = String::new();
        reader.read_line(&mut response_line)?;

        let response: CommandResponse = serde_json::from_str(&response_line.trim())?;

        // Update status with response
        self.status.code = response.exit_code;
        self.status.stdout = response.stdout;
        self.status.stderr = response.stderr;

        if response.success {
            debug!("Privileged command completed successfully");
            Ok(())
        } else {
            let error_msg = response.error.unwrap_or_else(|| {
                format!("Command failed with exit code: {}", response.exit_code)
            });
            Err(anyhow!(error_msg))
        }
    }

    /// Helper method to execute a command with given path and arguments
    fn exec_command(&mut self, command: &str, arguments: &[String]) -> anyhow::Result<()> {
        match std::process::Command::new(command)
            .envs(self.environment.clone())
            .args(arguments)
            .current_dir(&self.working_dir.clone().unwrap_or_else(|| {
                std::env::current_dir()
                    .map(|current_dir| current_dir.display().to_string())
                    .expect("Failed to get current directory")
            }))
            .output()
        {
            Ok(output) if output.status.success() => {
                self.status.stdout = String::from_utf8(output.stdout)?;
                self.status.stderr = String::from_utf8(output.stderr)?;
                self.status.code = output.status.code().unwrap_or(0);

                debug!("stdout: {}", &self.status.stdout);

                Ok(())
            }

            Ok(output) => {
                self.status.stdout = String::from_utf8(output.stdout)?;
                self.status.stderr = String::from_utf8(output.stderr)?;
                self.status.code = output.status.code().unwrap_or(-1);

                debug!("exit code: {}", &self.status.code);
                debug!("stdout: {}", &self.status.stdout);
                debug!("stderr: {}", &self.status.stderr);

                Err(anyhow!(
                    "Command failed with exit code: {}",
                    output.status.code().unwrap_or(1)
                ))
            }

            Err(err) => Err(anyhow!(err)),
        }
    }

    fn elevate_if_required(&self) -> (String, Vec<String>) {
        // Depending on the priviledged flag and who who the current user is
        // we can determine if we need to prepend sudo to the command

        let privilege_provider = self.privilege_provider.clone();

        match (self.privileged, whoami::username().as_str()) {
            // Hasn't requested priviledged, so never try to elevate
            (false, _) => (self.command.clone(), self.arguments.clone()),

            // Requested priviledged, but is already root
            (true, "root") => (self.command.clone(), self.arguments.clone()),

            // Requested priviledged, but is not root
            (true, _) => (
                privilege_provider,
                [vec![self.command.clone()], self.arguments.clone()].concat(),
            ),
        }
    }

    fn elevate(&mut self) -> anyhow::Result<()> {
        tracing::info!(
            "Privilege elevation required to run `{} {}`. Validating privileges ...",
            &self.command,
            &self.arguments.join(" ")
        );

        let privilege_provider = utilities::get_binary_path(&self.privilege_provider)?;

        match std::process::Command::new(privilege_provider)
            .stdin(std::process::Stdio::inherit())
            .stdout(std::process::Stdio::inherit())
            .stderr(std::process::Stdio::inherit())
            .arg("--validate")
            .output()
        {
            Ok(std::process::Output { status, .. }) if status.success() => Ok(()),

            Ok(std::process::Output { stderr, .. }) => Err(anyhow!(
                "Command requires privilege escalation, but couldn't elevate privileges: {}",
                String::from_utf8(stderr)?
            )),

            Err(err) => Err(anyhow!(
                "Command requires privilege escalation, but couldn't elevate privileges: {}",
                err
            )),
        }
    }
}

impl std::fmt::Display for Exec {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "CommandExec with: privileged={}: {} {}",
            self.privileged,
            self.command,
            self.arguments.join(" ")
        )
    }
}

impl Atom for Exec {
    fn plan(&self) -> anyhow::Result<Outcome> {
        Ok(Outcome {
            // Commands may have side-effects, but none that can be "known"
            // without some sandboxed operations to detect filesystem and network
            // affects.
            // Maybe we'll look into this one day?
            side_effects: vec![],
            // Commands should always run, we have no cache-key based
            // determinism atm the moment.
            should_run: true,
        })
    }

    fn execute(&mut self) -> anyhow::Result<()> {
        // If this command requires privileges and we have an elevated helper running,
        // send the command to the helper instead of using the old per-command elevation
        if self.privileged && std::env::var("COMTRYA_ELEVATED_PIPE").is_ok() {
            debug!("Command requires privileges, sending to elevated helper");
            return self.execute_via_elevated_helper();
        }

        // Fall back to the original per-command privilege escalation for backward compatibility
        // or when no elevated helper is available
        debug!("Using traditional per-command privilege escalation");

        let (command, arguments) = self.elevate_if_required();

        let command = utilities::get_binary_path(&command)
            .or_else(|_| Err(anyhow!("Command `{}` not found in path", command)))?;

        // If we require root, we need to use sudo with inherited IO
        // to ensure the user can respond if prompted for a password
        if command.eq("doas") || command.eq("sudo") || command.eq("run0") {
            match self.elevate() {
                Ok(_) => (),
                Err(err) => {
                    return Err(anyhow!(err));
                }
            }
        }

        self.exec_command(&command, &arguments)
    }

    fn output_string(&self) -> String {
        self.status.stdout.clone()
    }

    fn error_message(&self) -> String {
        self.status.stderr.clone()
    }

    fn as_any(&self) -> &dyn std::any::Any {
        self
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::contexts::privilege::Privilege;
    use pretty_assertions::assert_eq;

    #[test]
    fn defaults() {
        let command_run = Exec {
            ..Default::default()
        };

        assert_eq!(String::from(""), command_run.command);
        assert_eq!(0, command_run.arguments.len());
        assert_eq!(None, command_run.working_dir);
        assert_eq!(0, command_run.environment.len());
        assert_eq!(false, command_run.privileged);

        let command_run = new_run_command(String::from("echo"));

        assert_eq!(String::from("echo"), command_run.command);
        assert_eq!(0, command_run.arguments.len());
        assert_eq!(None, command_run.working_dir);
        assert_eq!(0, command_run.environment.len());
        assert_eq!(false, command_run.privileged);
    }

    #[test]
    fn elevate() {
        let mut command_run = new_run_command(String::from("echo"));
        command_run.arguments = vec![String::from("Hello, world!")];
        let (command, args) = command_run.elevate_if_required();

        assert_eq!(String::from("echo"), command);
        assert_eq!(vec![String::from("Hello, world!")], args);

        let mut command_run = new_run_command(String::from("echo"));
        command_run.arguments = vec![String::from("Hello, world!")];
        command_run.privileged = true;
        command_run.privilege_provider = Privilege::Sudo.to_string();
        let (command, args) = command_run.elevate_if_required();

        assert_eq!(String::from("sudo"), command);
        assert_eq!(
            vec![String::from("echo"), String::from("Hello, world!")],
            args
        );
    }

    #[test]
    fn elevate_doas() {
        let mut command_run = new_run_command(String::from("echo"));
        command_run.arguments = vec![String::from("Hello, world!")];
        let (command, args) = command_run.elevate_if_required();

        assert_eq!(String::from("echo"), command);
        assert_eq!(vec![String::from("Hello, world!")], args);

        let mut command_run = new_run_command(String::from("echo"));
        command_run.arguments = vec![String::from("Hello, world!")];
        command_run.privileged = true;
        command_run.privilege_provider = Privilege::Doas.to_string();
        let (command, args) = command_run.elevate_if_required();

        assert_eq!(String::from("doas"), command);
        assert_eq!(
            vec![String::from("echo"), String::from("Hello, world!")],
            args
        );
    }
    #[test]
    fn elevate_run0() {
        let mut command_run = new_run_command(String::from("echo"));
        command_run.arguments = vec![String::from("Hello, world!")];
        let (command, args) = command_run.elevate_if_required();

        assert_eq!(String::from("echo"), command);
        assert_eq!(vec![String::from("Hello, world!")], args);

        let mut command_run = new_run_command(String::from("echo"));
        command_run.arguments = vec![String::from("Hello, world!")];
        command_run.privileged = true;
        command_run.privilege_provider = Privilege::Run0.to_string();
        let (command, args) = command_run.elevate_if_required();

        assert_eq!(String::from("run0"), command);
        assert_eq!(
            vec![String::from("echo"), String::from("Hello, world!")],
            args
        );
    }

    #[test]
    fn error_propagation() {
        let mut command_run = new_run_command(String::from("non-existant-command"));
        command_run.execute().expect_err("Command should fail");
    }
}
